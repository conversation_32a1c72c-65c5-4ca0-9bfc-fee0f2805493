import { Enti<PERSON>, Player, Vector3 } from '@minecraft/server';
import { getRandomLocation } from '../../utilities/vector3';
import { EntityQuantityConfig, spawnEntitiesWithInterval } from '../../utilities/summonEntity';
import { getRandomInt } from '../../utilities/rng';

/**
 * Constants for the Breeze summoning event
 */
const MIN_BREEZES: number = 4; // Minimum number of breezes to summon
const MAX_BREEZES: number = 12; // Maximum number of breezes to summon
const SPAWN_RADIUS: number = 8; // Maximum distance from player to spawn breezes
const SPAWN_DELAY_TICKS: number = 5; // 0.25 seconds between spawns

/**
 * Event 70: It's a bit Breezy - Summons 4-12 breezes around the player location
 * Creates a chaotic environment with multiple breeze mobs attacking the player
 *
 * @param player - The player who triggered the event
 */
export function event70(player: Player): void {
  try {
    const dimension = player.dimension;
    const playerPos = player.location;

    // Determine how many breezes to spawn (random between MIN_BREEZES and MAX_BREEZES)
    const breezeCount = getRandomInt(MIN_BREEZES, MAX_BREEZES);

    // Configure the entity type and quantity to spawn
    const entityConfigs: EntityQuantityConfig[] = [{ entityId: 'minecraft:breeze', count: breezeCount }];

    // Initial effect to indicate the event is starting
    dimension.playSound('mob.breeze.shoot', playerPos, { pitch: 0.8, volume: 1.0 });
    dimension.spawnParticle('minecraft:ambient_dust_particle', playerPos);

    /**
     * Gets a random spawn location for breezes around the player
     * @returns A valid spawn location or undefined if none was found
     */
    const getSpawnLocation = (): Vector3 | undefined => {
      // Get a random location using the utility function
      return getRandomLocation(
        player.location,
        player.dimension,
        3, // Base offset (minimum distance from player)
        SPAWN_RADIUS - 3, // Additional random offset
        getRandomInt(-2, 3), // Y offset variation
        true // Check for air blocks
      );
    };

    /**
     * Callback function that runs when a breeze is spawned
     * Adds sound and particle effects to enhance the appearance
     */
    const onBreezeSpawned = (breeze: Entity): void => {
      if (!breeze || !breeze) return;

      // Play sound and particle effect at spawn location
      dimension.playSound('mob.breeze.ambient', breeze.location, {
        pitch: 1.0 + (Math.random() * 0.4 - 0.2),
        volume: 0.6
      });
      dimension.spawnParticle('minecraft:cauldron_explosion_emitter', breeze.location);
      dimension.playSound('mob.endermen.portal', breeze.location);
    };

    // Start spawning breezes with a delay between each
    spawnEntitiesWithInterval(
      dimension,
      entityConfigs,
      getSpawnLocation,
      SPAWN_DELAY_TICKS,
      onBreezeSpawned
    ).catch((_) => {
    });
  } catch (error) {
  }
  return;
}
