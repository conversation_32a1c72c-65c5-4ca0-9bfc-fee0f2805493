{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "ptd_lmhc:guidebook", "min_engine_version": "1.16.0", "materials": {"default": "entity_emissive_alpha", "page5": "guidebook_page5"}, "textures": {"book_cover": "textures/ptd/lm_hc/guidebook/book_cover", "page_1": "textures/ptd/lm_hc/guidebook/page_1", "page_2_introduction": "textures/ptd/lm_hc/guidebook/page_2_introduction", "page_3_crafting_lucky_pot": "textures/ptd/lm_hc/guidebook/page_3_crafting_lucky_pot", "page_4_cook_lucky_meal": "textures/ptd/lm_hc/guidebook/page_4_cook_lucky_meal", "page_5_when_you_eat": "textures/ptd/lm_hc/guidebook/page_5_when_you_eat", "page_6_world_achievements": "textures/ptd/lm_hc/guidebook/page_6_world_achievements", "achievements_page_1": "textures/ptd/lm_hc/guidebook/achievements_page_1", "achievements_page_2": "textures/ptd/lm_hc/guidebook/achievements_page_2", "achievements_page_3": "textures/ptd/lm_hc/guidebook/achievements_page_3", "achievements_page_4": "textures/ptd/lm_hc/guidebook/achievements_page_4", "achievements_page_5": "textures/ptd/lm_hc/guidebook/achievements_page_5", "achievements_page_6": "textures/ptd/lm_hc/guidebook/achievements_page_6", "achievements_page_7": "textures/ptd/lm_hc/guidebook/achievements_page_7", "achievements_page_8": "textures/ptd/lm_hc/guidebook/achievements_page_8", "achievements_page_9": "textures/ptd/lm_hc/guidebook/achievements_page_9", "achievements_page_10": "textures/ptd/lm_hc/guidebook/achievements_page_10", "achievements_page_11": "textures/ptd/lm_hc/guidebook/achievements_page_11", "achievements_page_12": "textures/ptd/lm_hc/guidebook/achievements_page_12", "achievements_page_13": "textures/ptd/lm_hc/guidebook/achievements_page_13", "achievements_page_14": "textures/ptd/lm_hc/guidebook/achievements_page_14", "achievements_page_15": "textures/ptd/lm_hc/guidebook/achievements_page_15", "lucky_meals_trophy": "textures/ptd/lm_hc/guidebook/lucky_meals_trophy", "achievement_locked": "textures/ptd/lm_hc/achievements/achievement_locked", "achievement_unlocked": "textures/ptd/lm_hc/achievements/achievement_unlocked", "event1": "textures/ptd/lm_hc/achievements/event1", "event2": "textures/ptd/lm_hc/achievements/event2", "event3": "textures/ptd/lm_hc/achievements/event3", "event4": "textures/ptd/lm_hc/achievements/event4", "event5": "textures/ptd/lm_hc/achievements/event5", "event6": "textures/ptd/lm_hc/achievements/event6", "event7": "textures/ptd/lm_hc/achievements/event7", "event8": "textures/ptd/lm_hc/achievements/event8", "event9": "textures/ptd/lm_hc/achievements/event9", "event10": "textures/ptd/lm_hc/achievements/event10", "event11": "textures/ptd/lm_hc/achievements/event11", "event12": "textures/ptd/lm_hc/achievements/event12", "event13": "textures/ptd/lm_hc/achievements/event13", "event14": "textures/ptd/lm_hc/achievements/event14", "event15": "textures/ptd/lm_hc/achievements/event15", "event16": "textures/ptd/lm_hc/achievements/event16", "event17": "textures/ptd/lm_hc/achievements/event17", "event18": "textures/ptd/lm_hc/achievements/event18", "event19": "textures/ptd/lm_hc/achievements/event19", "event20": "textures/ptd/lm_hc/achievements/event20", "event21": "textures/ptd/lm_hc/achievements/event21", "event22": "textures/ptd/lm_hc/achievements/event22", "event23": "textures/ptd/lm_hc/achievements/event23", "event24": "textures/ptd/lm_hc/achievements/event24", "event25": "textures/ptd/lm_hc/achievements/event25", "event26": "textures/ptd/lm_hc/achievements/event26", "event27": "textures/ptd/lm_hc/achievements/event27", "event28": "textures/ptd/lm_hc/achievements/event28", "event29": "textures/ptd/lm_hc/achievements/event29", "event30": "textures/ptd/lm_hc/achievements/event30", "event31": "textures/ptd/lm_hc/achievements/event31", "event32": "textures/ptd/lm_hc/achievements/event32", "event33": "textures/ptd/lm_hc/achievements/event33", "event34": "textures/ptd/lm_hc/achievements/event34", "event35": "textures/ptd/lm_hc/achievements/event35", "event36": "textures/ptd/lm_hc/achievements/event36", "event37": "textures/ptd/lm_hc/achievements/event37", "event38": "textures/ptd/lm_hc/achievements/event38", "event39": "textures/ptd/lm_hc/achievements/event39", "event40": "textures/ptd/lm_hc/achievements/event40", "event41": "textures/ptd/lm_hc/achievements/event41", "event42": "textures/ptd/lm_hc/achievements/event42", "event43": "textures/ptd/lm_hc/achievements/event43", "event44": "textures/ptd/lm_hc/achievements/event44", "event45": "textures/ptd/lm_hc/achievements/event45", "event46": "textures/ptd/lm_hc/achievements/event46", "event47": "textures/ptd/lm_hc/achievements/event47", "event48": "textures/ptd/lm_hc/achievements/event48", "event49": "textures/ptd/lm_hc/achievements/event49", "event50": "textures/ptd/lm_hc/achievements/event50", "event51": "textures/ptd/lm_hc/achievements/event51", "event52": "textures/ptd/lm_hc/achievements/event52", "event53": "textures/ptd/lm_hc/achievements/event53", "event54": "textures/ptd/lm_hc/achievements/event54", "event55": "textures/ptd/lm_hc/achievements/event55", "event56": "textures/ptd/lm_hc/achievements/event56", "event57": "textures/ptd/lm_hc/achievements/event57", "event58": "textures/ptd/lm_hc/achievements/event58", "event59": "textures/ptd/lm_hc/achievements/event59", "event60": "textures/ptd/lm_hc/achievements/event60", "event61": "textures/ptd/lm_hc/achievements/event61", "event62": "textures/ptd/lm_hc/achievements/event62", "event63": "textures/ptd/lm_hc/achievements/event63", "event64": "textures/ptd/lm_hc/achievements/event64", "event65": "textures/ptd/lm_hc/achievements/event65", "event66": "textures/ptd/lm_hc/achievements/event66", "event67": "textures/ptd/lm_hc/achievements/event67", "event68": "textures/ptd/lm_hc/achievements/event68", "event69": "textures/ptd/lm_hc/achievements/event69", "event70": "textures/ptd/lm_hc/achievements/event70", "event71": "textures/ptd/lm_hc/achievements/event71", "event72": "textures/ptd/lm_hc/achievements/event72", "event73": "textures/ptd/lm_hc/achievements/event73", "event74": "textures/ptd/lm_hc/achievements/event74", "event75": "textures/ptd/lm_hc/achievements/event75"}, "geometry": {"default": "geometry.ptd_lmhc.ptd_lmhc_guidebook"}, "animations": {"face_player": "animation.ptd_lmhc_guidebook.face_player"}, "scripts": {"scale": "q.distance_from_camera < 3 ? 1 : 0", "animate": ["face_player"]}, "render_controllers": ["controller.render.ptd_lmhc.ptd_lmhc_guidebook", {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event1": "q.property('ptd_lmhc:pages') == 7"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event2": "q.property('ptd_lmhc:pages') == 7"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event3": "q.property('ptd_lmhc:pages') == 7"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event4": "q.property('ptd_lmhc:pages') == 7"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event5": "q.property('ptd_lmhc:pages') == 7"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event6": "q.property('ptd_lmhc:pages') == 8"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event7": "q.property('ptd_lmhc:pages') == 8"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event8": "q.property('ptd_lmhc:pages') == 8"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event9": "q.property('ptd_lmhc:pages') == 8"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event10": "q.property('ptd_lmhc:pages') == 8"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event11": "q.property('ptd_lmhc:pages') == 9"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event12": "q.property('ptd_lmhc:pages') == 9"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event13": "q.property('ptd_lmhc:pages') == 9"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event14": "q.property('ptd_lmhc:pages') == 9"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event15": "q.property('ptd_lmhc:pages') == 9"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event16": "q.property('ptd_lmhc:pages') == 10"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event17": "q.property('ptd_lmhc:pages') == 10"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event18": "q.property('ptd_lmhc:pages') == 10"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event19": "q.property('ptd_lmhc:pages') == 10"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event20": "q.property('ptd_lmhc:pages') == 10"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event21": "q.property('ptd_lmhc:pages') == 11"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event22": "q.property('ptd_lmhc:pages') == 11"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event23": "q.property('ptd_lmhc:pages') == 11"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event24": "q.property('ptd_lmhc:pages') == 11"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event25": "q.property('ptd_lmhc:pages') == 11"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event26": "q.property('ptd_lmhc:pages') == 12"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event27": "q.property('ptd_lmhc:pages') == 12"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event28": "q.property('ptd_lmhc:pages') == 12"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event29": "q.property('ptd_lmhc:pages') == 12"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event30": "q.property('ptd_lmhc:pages') == 12"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event31": "q.property('ptd_lmhc:pages') == 13"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event32": "q.property('ptd_lmhc:pages') == 13"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event33": "q.property('ptd_lmhc:pages') == 13"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event34": "q.property('ptd_lmhc:pages') == 13"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event35": "q.property('ptd_lmhc:pages') == 13"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event36": "q.property('ptd_lmhc:pages') == 14"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event37": "q.property('ptd_lmhc:pages') == 14"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event38": "q.property('ptd_lmhc:pages') == 14"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event39": "q.property('ptd_lmhc:pages') == 14"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event40": "q.property('ptd_lmhc:pages') == 14"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event41": "q.property('ptd_lmhc:pages') == 15"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event42": "q.property('ptd_lmhc:pages') == 15"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event43": "q.property('ptd_lmhc:pages') == 15"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event44": "q.property('ptd_lmhc:pages') == 15"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event45": "q.property('ptd_lmhc:pages') == 15"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event46": "q.property('ptd_lmhc:pages') == 16"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event47": "q.property('ptd_lmhc:pages') == 16"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event48": "q.property('ptd_lmhc:pages') == 16"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event49": "q.property('ptd_lmhc:pages') == 16"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event50": "q.property('ptd_lmhc:pages') == 16"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event51": "q.property('ptd_lmhc:pages') == 17"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event52": "q.property('ptd_lmhc:pages') == 17"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event53": "q.property('ptd_lmhc:pages') == 17"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event54": "q.property('ptd_lmhc:pages') == 17"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event55": "q.property('ptd_lmhc:pages') == 17"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event56": "q.property('ptd_lmhc:pages') == 18"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event57": "q.property('ptd_lmhc:pages') == 18"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event58": "q.property('ptd_lmhc:pages') == 18"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event59": "q.property('ptd_lmhc:pages') == 18"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event60": "q.property('ptd_lmhc:pages') == 18"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event61": "q.property('ptd_lmhc:pages') == 19"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event62": "q.property('ptd_lmhc:pages') == 19"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event63": "q.property('ptd_lmhc:pages') == 19"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event64": "q.property('ptd_lmhc:pages') == 19"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event65": "q.property('ptd_lmhc:pages') == 19"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event66": "q.property('ptd_lmhc:pages') == 20"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event67": "q.property('ptd_lmhc:pages') == 20"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event68": "q.property('ptd_lmhc:pages') == 20"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event69": "q.property('ptd_lmhc:pages') == 20"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event70": "q.property('ptd_lmhc:pages') == 20"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event71": "q.property('ptd_lmhc:pages') == 21"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event72": "q.property('ptd_lmhc:pages') == 21"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event73": "q.property('ptd_lmhc:pages') == 21"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event74": "q.property('ptd_lmhc:pages') == 21"}, {"controller.render.ptd_lmhc.ptd_lmhc_guidebook_event75": "q.property('ptd_lmhc:pages') == 21"}]}}}