{"format_version": "1.10.0", "animations": {"animation.ptd_lmhc.food_limesicle_pop.hold_third_person": {"loop": true, "bones": {"root": {"rotation": [-103.24115, -54.93728, 13.05852], "position": [3, 20, -8.5], "scale": 0.6}}}, "animation.ptd_lmhc.food_limesicle_pop.eat": {"loop": "hold_on_last_frame", "animation_length": 1.6, "bones": {"root": {"position": {"0.0": [0, 0, 0], "0.0833": [-0.37, -0.37, 0.44], "0.1667": [-0.77, 0, 0], "0.25": [-0.94, 3.06, -3.5], "0.3333": [-1, 6, -7], "0.4167": [-1, 5, -7], "0.5": [-1, 6, -7], "0.5833": [-1, 5, -7], "0.6667": [-1, 6, -7], "0.75": [-1, 5, -7], "0.8333": [-1, 6, -7], "0.9167": [-1, 5, -7], "1.0": [-1, 6, -7], "1.0833": [-1, 5, -7], "1.1667": [-1, 6, -7], "1.25": [-1, 5, -7], "1.3333": [-1, 6, -7], "1.4167": [-1, 5, -7], "1.5": [-1, 6, -7], "1.5833": [-1, 5, -7]}, "scale": [0.5, 0.5, 1]}}}, "animation.ptd_lmhc.food_limesicle_pop.hold_first_person": {"loop": true, "bones": {"root": {"rotation": [-41.43969, -20.69763, -31.64052], "position": [-6, 24, -10]}}}}}