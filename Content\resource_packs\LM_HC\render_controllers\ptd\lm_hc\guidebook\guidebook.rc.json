{"format_version": "1.8.0", "render_controllers": {"controller.render.ptd_lmhc.ptd_lmhc_guidebook": {"arrays": {"textures": {"Array.pages": ["texture.book_cover", "texture.page_1", "texture.page_2_introduction", "texture.page_3_crafting_lucky_pot", "texture.page_4_cook_lucky_meal", "texture.page_5_when_you_eat", "texture.page_6_world_achievements", "texture.achievements_page_1", "texture.achievements_page_2", "texture.achievements_page_3", "texture.achievements_page_4", "texture.achievements_page_5", "texture.achievements_page_6", "texture.achievements_page_7", "texture.achievements_page_8", "texture.achievements_page_9", "texture.achievements_page_10", "texture.achievements_page_11", "texture.achievements_page_12", "texture.achievements_page_13", "texture.achievements_page_14", "texture.achievements_page_15", "texture.lucky_meals_trophy"]}}, "geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "q.property('ptd_lmhc:pages') == 4 ? Material.page5 : Material.default"}], "textures": ["Array.pages[q.property('ptd_lmhc:pages')]"], "uv_anim": {"offset": [0, "q.property('ptd_lmhc:pages') == 4 ? math.mod(math.floor(query.life_time * 2), 7) / 7 : 0"], "scale": [1, "q.property('ptd_lmhc:pages') == 4 ? 1 / 7 : 1"]}, "part_visibility": [{"*": true}, {"achievement1": false}, {"achievement2": false}, {"achievement3": false}, {"achievement4": false}, {"achievement5": false}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event1": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page7')), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement1": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event2": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page7') / 2), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement2": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event3": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page7') / 4), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement3": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event4": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page7') / 8), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement4": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event5": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page7') / 16), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement5": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event6": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page8')), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement1": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event7": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page8') / 2), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement2": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event8": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page8') / 4), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement3": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event9": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page8') / 8), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement4": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event10": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page8') / 16), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement5": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event11": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page9')), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement1": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event12": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page9') / 2), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement2": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event13": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page9') / 4), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement3": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event14": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page9') / 8), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement4": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event15": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page9') / 16), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement5": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event16": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page10')), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement1": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event17": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page10') / 2), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement2": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event18": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page10') / 4), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement3": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event19": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page10') / 8), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement4": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event20": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page10') / 16), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement5": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event21": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page11')), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement1": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event22": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page11') / 2), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement2": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event23": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page11') / 4), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement3": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event24": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page11') / 8), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement4": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event25": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page11') / 16), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement5": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event26": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page12')), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement1": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event27": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page12') / 2), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement2": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event28": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page12') / 4), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement3": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event29": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page12') / 8), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement4": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event30": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page12') / 16), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement5": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event31": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page13')), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement1": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event32": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page13') / 2), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement2": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event33": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page13') / 4), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement3": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event34": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page13') / 8), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement4": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event35": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page13') / 16), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement5": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event36": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page14')), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement1": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event37": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page14') / 2), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement2": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event38": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page14') / 4), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement3": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event39": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page14') / 8), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement4": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event40": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page14') / 16), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement5": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event41": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page15')), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement1": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event42": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page15') / 2), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement2": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event43": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page15') / 4), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement3": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event44": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page15') / 8), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement4": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event45": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page15') / 16), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement5": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event46": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page16')), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement1": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event47": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page16') / 2), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement2": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event48": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page16') / 4), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement3": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event49": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page16') / 8), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement4": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event50": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page16') / 16), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement5": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event51": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page17')), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement1": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event52": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page17') / 2), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement2": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event53": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page17') / 4), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement3": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event54": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page17') / 8), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement4": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event55": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page17') / 16), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement5": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event56": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page18')), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement1": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event57": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page18') / 2), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement2": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event58": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page18') / 4), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement3": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event59": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page18') / 8), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement4": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event60": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page18') / 16), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement5": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event61": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page19')), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement1": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event62": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page19') / 2), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement2": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event63": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page19') / 4), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement3": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event64": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page19') / 8), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement4": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event65": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page19') / 16), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement5": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event66": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page20')), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement1": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event67": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page20') / 2), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement2": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event68": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page20') / 4), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement3": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event69": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page20') / 8), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement4": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event70": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page20') / 16), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement5": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event71": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page21')), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement1": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event72": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page21') / 2), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement2": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event73": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page21') / 4), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement3": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event74": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page21') / 8), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement4": true}]}, "controller.render.ptd_lmhc.ptd_lmhc_guidebook_event75": {"geometry": "geometry.ptd_lmhc.default", "materials": [{"*": "Material.default"}], "textures": ["math.mod(math.floor(q.property('ptd_lmhc:achievements_page21') / 16), 2) == 1 ? Texture.achievement_unlocked : Texture.achievement_locked"], "part_visibility": [{"*": false}, {"achievement5": true}]}}}