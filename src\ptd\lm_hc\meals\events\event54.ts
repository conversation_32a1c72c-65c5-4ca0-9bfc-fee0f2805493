import { Player, Vector3 } from '@minecraft/server';
import { getRandomLocation } from '../../utilities/vector3';
import { getRandomInt } from '../../utilities/rng';
import { EntityQuantityConfig, spawnEntitiesWithInterval } from '../../utilities/summonEntity';

/**
 * Event 54: Sudden Night
 * Instantly turns the world to night, spawning phantoms, mobs, and adds darkness effect
 * @param player The player who triggered the event
 */
export function event54(player: Player): void {
  try {
    // Set time to night (13000 ticks = night)
    player.runCommand('time set midnight');

    // Apply darkness effect for 20 seconds (400 ticks)
    player.addEffect('darkness', 400, { amplifier: 1, showParticles: true });

    // Define the mobs to spawn
    const hostileMobs: string[] = [
      'minecraft:phantom',
      'minecraft:zombie',
      'minecraft:skeleton',
      'minecraft:spider',
      'minecraft:creeper'
    ] as const;

    // Number of phantoms to spawn (fixed count)
    const phantomCount = 3;

    // Random number of other hostile mobs to spawn (8-16)
    const otherMobCount = getRandomInt(8, 16);

    // Spawn phantoms
    for (let i = 0; i < phantomCount; i++) {
      const spawnPos: Vector3 | undefined = getRandomLocation(
        player.location,
        player.dimension,
        0, // base offset
        getRandomInt(-8, 8), // horizontal offset
        getRandomInt(5, 10), // vertical offset - above player
        true // check for air block
      );

      if (spawnPos) {
        // Spawn phantom
        player.dimension.spawnEntity('minecraft:phantom', spawnPos);

        // Play particle and sound effects
        player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', spawnPos);
        player.dimension.playSound('mob.endermen.portal', spawnPos);
      }
    }

    // Prepare entity configurations for other hostile mobs
    // Evenly distribute the mob count across different mob types (excluding phantoms)
    const mobTypesToSpawn = hostileMobs.slice(1); // Exclude phantoms (index 0)
    const mobsPerType = Math.floor(otherMobCount / mobTypesToSpawn.length);
    const remainder = otherMobCount % mobTypesToSpawn.length;

    // Create entity configurations
    const entityConfigs: EntityQuantityConfig[] = mobTypesToSpawn.map((mobType, index) => {
      // Add the remainder mobs to the first few mob types to ensure we spawn exactly otherMobCount
      const count = index < remainder ? mobsPerType + 1 : mobsPerType;
      return {
        entityId: mobType,
        count: count
      };
    });

    // Define the location callback function
    const getSpawnLocation = (): Vector3 | undefined => {
      return getRandomLocation(
        player.location,
        player.dimension,
        1, // base offset
        getRandomInt(-10, 10), // horizontal offset
        0, // vertical offset (ground level)
        true // check for air block
      );
    };

    // Define the entity spawned callback function
    const onEntitySpawned = (_entity: any, _entityId: string): void => {
      const spawnPos = _entity.location;
      // Play particle and sound effects
      player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', spawnPos);
      player.dimension.playSound('mob.endermen.portal', spawnPos);
    };

    // Spawn the other hostile mobs with interval
    spawnEntitiesWithInterval(
      player.dimension,
      entityConfigs,
      getSpawnLocation,
      10, // Delay of 10 ticks (0.5 seconds) between spawns
      onEntitySpawned
    );
  } catch (error) {
  }
  return;
}
