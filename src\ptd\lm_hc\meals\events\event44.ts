import { EntityComponentTypes, EntityInventoryComponent, ItemStack, Player } from '@minecraft/server';

/**
 * Event 44: Unlucky Elixir
 * Creates a mysterious potion that applies negative effects when consumed.
 * The potion appears identical to the Lucky Elixir (event 23) to create
 * an element of surprise and risk for the player.
 * @param player The player who triggered the event
 */
export function event44(player: Player): void {
  try {
    // Create an Unlucky Elixir item stack using the same item as Lucky Elixir
    const unluckyElixir = new ItemStack('ptd_lmhc:lucky_elixir', 1);

    // Set the same name as Lucky Elixir to disguise it
    unluckyElixir.nameTag = '§b§lLucky Elixir';

    // Add mysterious lore that doesn't reveal whether it's good or bad
    unluckyElixir.setLore([
      '§dA mysterious potion with unpredictable effects',
      '§7The liquid swirls with strange energy',
      '§7Only the brave would dare to drink it'
    ]);

    // Set a dynamic property to identify this as an unlucky elixir
    // This will be used to determine which effects to apply when consumed
    unluckyElixir.setDynamicProperty('ptd_lmhc:is_unlucky_elixir', true);

    // Give the Unlucky Elixir to the player
    const inventory = player.getComponent(EntityComponentTypes.Inventory) as EntityInventoryComponent;
    if (inventory) {
      const container = inventory.container;
      if (container) {
        container.addItem(unluckyElixir);
      }
    }

    // Play sound to indicate item received - same as Lucky Elixir
    player.playSound('random.levelup', { volume: 0.5, pitch: 1.0 });

    // Spawn particles for visual effect - same as Lucky Elixir
    for (let i = 0; i < 10; i++) {
      player.dimension.spawnParticle('minecraft:endrod', {
        x: player.location.x,
        y: player.location.y + 1,
        z: player.location.z
      });
    }
  } catch (error) {
  }
  return;
}

/**
 * Unlucky Elixir: A special potion that applies wither, nausea, poison, and slowness effects
 * when consumed. Disguised to look identical to the Lucky Elixir.
 * @param player The player who consumed the Unlucky Elixir
 */
export function handleUnluckyElixirConsume(player: Player): void {
  try {
    // Apply Wither effect for 30 seconds (600 ticks)
    player.addEffect('wither', 600, {
      amplifier: 1, // Wither II
      showParticles: true
    });

    // Apply Nausea effect for 45 seconds (900 ticks)
    player.addEffect('nausea', 900, {
      amplifier: 0,
      showParticles: true
    });

    // Apply Poison effect for 30 seconds (600 ticks)
    player.addEffect('poison', 600, {
      amplifier: 0,
      showParticles: true
    });

    // Apply Slowness effect for 45 seconds (900 ticks)
    player.addEffect('slowness', 900, {
      amplifier: 1, // Slowness II
      showParticles: true
    });

    // Play sound effect
    player.playSound('random.glass', { volume: 0.7, pitch: 0.5 });
  } catch (error) {
  }
  return;
}
