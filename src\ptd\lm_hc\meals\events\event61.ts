import {
  Dimension,
  EntityComponentTypes,
  EntityDamageCause,
  EntityIsTamedComponent,
  Player,
  Vector3
} from '@minecraft/server';
import { fixedLenRaycast } from '../../utilities/raycasts';
import { PROTECTED_BLOCKS } from '../../utilities/constants/protectedBlocks';
import { excludedEntitiesPlayerForAll } from '../../utilities/entityQueries';

/**
 * Constants for the Sonic Boom effect
 */
const SONIC_BOOM_LENGTH: number = 16; // Maximum distance in blocks the sonic boom will travel
const SONIC_BOOM_DAMAGE: number = 80; // Damage to entities hit by the sonic boom
const STEP: number = 1; // Distance between each raycast point
const ENTITY_DETECTION_RADIUS: number = 4; // Radius to check for entities to damage

/**
 * Destroys blocks in a 3x3x3 area around the given position
 * @param pos - Center position for block destruction
 * @param dimension - The dimension to destroy blocks in
 */
function destroyBlocksAround(pos: Vector3, dimension: Dimension): void {
  for (let x = -1; x <= 1; x++) {
    for (let y = -1; y <= 1; y++) {
      for (let z = -1; z <= 1; z++) {
        const blockPos = {
          x: Math.floor(pos.x) + x,
          y: Math.floor(pos.y) + y,
          z: Math.floor(pos.z) + z
        };
        try {
          const blockTypeId = dimension.getBlock(blockPos)?.type.id;
          if (blockTypeId && PROTECTED_BLOCKS.has(blockTypeId)) continue;
          dimension.runCommand(`setblock ${blockPos.x} ${blockPos.y} ${blockPos.z} air [] destroy`);
        } catch (error) {
        }
      }
    }
  }
  return;
}

/**
 * Event 61: Sonic Boom - Summons a Warden ability where the player is facing
 *
 * @param player - The player who triggered the event
 *
 * @remarks
 * This function:
 * - Creates a raycast from the player's head position in their view direction
 * - Spawns sonic boom particles along the raycast path
 * - Damages non-tamed entities in range of the sonic boom
 * - Plays the warden's sonic boom sound effect
 * - Destroys non-protected blocks in the sonic boom's path
 *
 * The sonic boom:
 * - Extends for 16 blocks
 * - Deals 80 damage to entities
 * - Affects entities within 4 blocks of the boom path
 * - Does not damage tamed entities
 * - Does not damage the player who created it
 */
export function event61(player: Player): void {
  try {
    if (!player || !player) {
      return;
    }

    const dimension: Dimension = player.dimension;
    const startPos: Vector3 = player.getHeadLocation();
    const viewDirection: Vector3 = player.getViewDirection();

    // Normalize the view direction vector
    const magnitude = Math.sqrt(viewDirection.x ** 2 + viewDirection.y ** 2 + viewDirection.z ** 2);
    const normalizedDir: Vector3 = {
      x: viewDirection.x / magnitude,
      y: viewDirection.y / magnitude,
      z: viewDirection.z / magnitude
    };

    // Get all positions along the raycast
    const positions = fixedLenRaycast(startPos, normalizedDir, SONIC_BOOM_LENGTH, STEP);

    // Play sound effect
    dimension.playSound('mob.warden.sonic_boom', startPos);
    player.runCommand('camerashake add @s 0.5 1 positional');

    // Process each position for particles and entity damage
    for (const pos of positions) {
      // Spawn particles
      try {
        dimension.spawnParticle('minecraft:sonic_explosion', pos);

        // Destroy blocks around the position
        destroyBlocksAround(pos, dimension);

        // Damage nearby entities
        const entities = dimension.getEntities({
          ...excludedEntitiesPlayerForAll,
          location: pos,
          maxDistance: ENTITY_DETECTION_RADIUS
        });

        entities.forEach((entity) => {
          if (entity.id !== player.id) {
            try {
              const isTamed = entity.getComponent(EntityComponentTypes.IsTamed) as EntityIsTamedComponent;
              if (!isTamed) {
                entity.applyDamage(SONIC_BOOM_DAMAGE, {
                  cause: EntityDamageCause.sonicBoom,
                  damagingEntity: player
                });
              }
            } catch (error) {
            }
          }
        });
      } catch (error) {
      }
    }
  } catch (error) {
  }
  return;
}
