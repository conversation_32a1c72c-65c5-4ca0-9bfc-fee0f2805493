import { Player, Vector3, ItemStack } from '@minecraft/server';
import { getRandomLocation } from '../../utilities/vector3';

/**
 * Event 15: <PERSON><PERSON> Slayer - Instantly drops 3 nether stars above the player
 * Uses getRandomLocation for a more natural and spread out item drop pattern
 *
 * @param player - The player who triggered the event
 */
export function event15(player: Player): void {
  try {
    const playerPos: Vector3 = player.location;
    const spawnOffsets: Array<Vector3> = [];

    // Create 3 nether stars with slightly randomized positions
    for (let i = 0; i < 3; i++) {
      const spawnLoc: Vector3 | undefined = getRandomLocation(
        playerPos,
        player.dimension,
        2, // Base offset for spread
        1, // Additional random offset
        3, // Y offset above player
        true // Check for air blocks since we're spawning items
      );

      if (spawnLoc) {
        // Create and spawn the nether star
        const netherStar: ItemStack = new ItemStack('minecraft:nether_star', 1);
        player.dimension.spawnItem(netherStar, spawnLoc);

        // Track spawn location for particle effects
        spawnOffsets.push(spawnLoc);
      }
    }

    // Visual and audio feedback
    spawnOffsets.forEach((loc) => {
      player.dimension.spawnParticle('minecraft:totem_particle', loc);
    });
    player.playSound('random.levelup');
  } catch (error) {
  }
  return;
}
