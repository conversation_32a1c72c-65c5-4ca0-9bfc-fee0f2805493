/**
 * Module for handling Lucky Food Trophy entity interactions and behaviors.
 * The Lucky Food Trophy is a special decorative item crafted from golden equipment.
 */
import { ItemStack } from '@minecraft/server';
import { getDistance } from '../utilities/vector3';
/**
 * Dynamic property key for tracking trophy state
 * @constant {string}
 */
const TROPHY_STATE_PROPERTY = 'ptd_lmhc:trophy_state';
/**
 * Resets all properties of the Lucky Food Trophy to their initial state
 * @param trophy - The Lucky Food Trophy entity to reset
 */
function resetTrophyProperties(trophy) {
    try {
        trophy.setDynamicProperty(TROPHY_STATE_PROPERTY, '{}');
        trophy.setProperty('ptd_lmhc:times_to_destroy', 0);
    }
    catch (error) {
    }
}
/**
 * Handles the destruction of a Lucky Food Trophy entity.
 * The trophy must be hit 7 times before it can be collected as an item.
 *
 * @param trophy - The Lucky Food Trophy entity being destroyed
 * @param player - The player destroying the trophy
 *
 * @remarks
 * - Tracks destruction progress using the 'ptd_lmhc:times_to_destroy' property
 * - When fully destroyed, converts into an item and launches toward the player
 * - Plays a swing animation on each hit
 * - Cleans up dynamic properties on destruction
 */
export function destroyLuckyFoodTrophy(trophy, player) {
    try {
        const timesToDestroy = trophy.getProperty('ptd_lmhc:times_to_destroy');
        const spawnPos = trophy.location;
        // Play the swing animation
        trophy.playAnimation('animation.lucky_food_trophy.swing', { blendOutTime: 0.1 });
        // Handle destroy trophy
        if (timesToDestroy >= 7) {
            // Stop sounds when the trophy is actually being destroyed
            trophy.runCommand('stopsound @a[r=11] pot.idle_2');
            try {
                // Remove light block at trophy location
                try {
                    const blockPos = {
                        x: Math.floor(trophy.location.x),
                        y: Math.floor(trophy.location.y),
                        z: Math.floor(trophy.location.z)
                    };
                    const block = trophy.dimension.getBlock(blockPos);
                    if (block && block.typeId === 'minecraft:light_block_12') {
                        block.setType('minecraft:air');
                    }
                }
                catch (error) {
                }
                // Always clean up dynamic properties before destruction
                resetTrophyProperties(trophy);
                // Calculate direction vector from trophy to player
                const directionToPlayer = {
                    x: player.location.x - spawnPos.x,
                    y: player.location.y - spawnPos.y,
                    z: player.location.z - spawnPos.z
                };
                // Normalize the direction vector based on distance
                const distance = getDistance(spawnPos, player.location);
                const normalizedDirection = {
                    x: (directionToPlayer.x / distance) * 0.1,
                    y: 0.15,
                    z: (directionToPlayer.z / distance) * 0.1
                };
                // Spawn the trophy item
                const trophyItem = new ItemStack('ptd_lmhc:lucky_food_trophy');
                trophy.dimension.spawnItem(trophyItem, spawnPos).applyImpulse(normalizedDirection);
                // Remove the trophy
                trophy.remove();
            }
            catch (error) {
                // Ensure properties are reset even if destruction fails
                resetTrophyProperties(trophy);
                // Still try to remove the trophy
                try {
                    trophy.remove();
                }
                catch { }
            }
        }
    }
    catch (error) {
        // Try one last time to clean up
        try {
            resetTrophyProperties(trophy);
            trophy.remove();
        }
        catch { }
    }
}
