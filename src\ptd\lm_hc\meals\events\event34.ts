import { BlockPermutation, Player, Vector3 } from '@minecraft/server';

/**
 * Event 34. Obsidian Cage: Traps the player in a 3x3x3 obsidian cage with an escape hole on top.
 * Creates walls of obsidian blocks around a 3x3x3 inner space with a one-block opening in the center of the ceiling.
 * The cage is designed to be challenging but escapable, providing a strategic gameplay element.
 *
 * @param player - The player object to be trapped in the obsidian cage
 * @throws Will log a warning message if the event execution fails
 */
export function event34(player: Player): void {
  try {
    const obsidian: BlockPermutation = BlockPermutation.resolve('minecraft:obsidian');
    const playerLoc: Vector3 = {
      x: Math.floor(player.location.x),
      y: Math.floor(player.location.y),
      z: Math.floor(player.location.z)
    };

    // Create bottom layer (5x5)
    for (let x = -2; x <= 2; x++) {
      for (let z = -2; z <= 2; z++) {
        player.dimension
          .getBlock({ x: playerLoc.x + x, y: playerLoc.y - 1, z: playerLoc.z + z })
          ?.setPermutation(obsidian);
      }
    }

    // Create walls (3 blocks high)
    for (let y = 0; y <= 2; y++) {
      for (let x = -2; x <= 2; x++) {
        for (let z = -2; z <= 2; z++) {
          // Only place blocks on the outer perimeter to create 3x3x3 inner space
          if (Math.abs(x) === 2 || Math.abs(z) === 2) {
            player.dimension
              .getBlock({ x: playerLoc.x + x, y: playerLoc.y + y, z: playerLoc.z + z })
              ?.setPermutation(obsidian);
          }
        }
      }
    }

    // Create top layer with center hole (5x5)
    for (let x = -2; x <= 2; x++) {
      for (let z = -2; z <= 2; z++) {
        // Skip the center block to create the escape hole
        if (x === 0 && z === 0) continue;
        player.dimension
          .getBlock({ x: playerLoc.x + x, y: playerLoc.y + 3, z: playerLoc.z + z })
          ?.setPermutation(obsidian);
      }
    }

    // Add particle effects for dramatic effect
    player.dimension.spawnParticle('minecraft:large_explosion', playerLoc);
  } catch (error) {
  }
  return;
}
