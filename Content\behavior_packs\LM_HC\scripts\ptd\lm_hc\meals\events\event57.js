import { getRandomLocation } from '../../utilities/vector3';
/**
 * Event 57: Mega Villager
 * Spawns a custom villager with ridiculously cheap trades
 * The villager offers valuable items for just 1 emerald or even free trades
 *
 * Finds a safe air block near the player to spawn the villager
 * Base offset: 2 blocks
 * Additional random offset: 1 block
 * Y offset: 0 blocks (same level as player)
 *
 * @param player - The player who triggered the event
 * @throws Will throw if unable to access player dimension or location, or if no safe spawn location is found
 */
export function event57(player) {
    try {
        const dimension = player.dimension;
        const playerPos = player.location;
        // Find a safe location near the player with air block validation
        const spawnPos = getRandomLocation(playerPos, dimension, 2, 1, 0, true);
        if (!spawnPos) {
            return;
        }
        // Spawn the special villager with extremely cheap trades
        const megaVillager = dimension.spawnEntity('ptd_lmhc:mega_villager', spawnPos);
        dimension.spawnParticle('minecraft:villager_happy', spawnPos);
        dimension.playSound('mob.villager.haggle', spawnPos);
        // Set a colored name tag for the mega villager (using Minecraft's formatting codes)
        // §2 = Dark Green, §a = Green, §e = Yellow
        megaVillager.nameTag = '§2⚡ §aM§ee§ag§ea §aV§ei§al§el§aa§eg§ae§er §2⚡§r';
    }
    catch (error) {
    }
    return;
}
