{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "ptd_lmhc:mega_villager", "materials": {"default": "villager_v2", "masked": "villager_v2_masked", "enchanted": "enchanted_entity"}, "textures": {"base": "textures/entity/villager2/villager", "base2": "textures/entity/villager2/villager", "base3": "textures/entity/villager2/villager", "base4": "textures/entity/villager2/villager", "base5": "textures/entity/villager2/villager", "base6": "textures/entity/villager2/villager", "desert": "textures/entity/villager2/biomes/biome_desert", "jungle": "textures/entity/villager2/biomes/biome_jungle", "plains": "textures/entity/villager2/biomes/biome_plains", "savanna": "textures/entity/villager2/biomes/biome_savanna", "snow": "textures/entity/villager2/biomes/biome_snow", "swamp": "textures/entity/villager2/biomes/biome_swamp", "taiga": "textures/entity/villager2/biomes/biome_taiga", "armorer": "textures/entity/villager2/professions/armorer", "butcher": "textures/entity/villager2/professions/butcher", "cartographer": "textures/entity/villager2/professions/cartographer", "cleric": "textures/entity/villager2/professions/cleric", "farmer": "textures/entity/villager2/professions/farmer", "fisherman": "textures/entity/villager2/professions/fisherman", "fletcher": "textures/entity/villager2/professions/fletcher", "leatherworker": "textures/entity/villager2/professions/leatherworker", "librarian": "textures/entity/villager2/professions/librarian", "shepherd": "textures/entity/villager2/professions/shepherd", "tool_smith": "textures/entity/villager2/professions/toolsmith", "weapon_smith": "textures/entity/villager2/professions/weaponsmith", "stonemason": "textures/entity/villager2/professions/stonemason", "nitwit": "textures/entity/villager2/professions/nitwit", "unskilled": "textures/entity/villager2/professions/unskilled", "level_stone": "textures/entity/villager2/levels/level_stone", "level_iron": "textures/entity/villager2/levels/level_iron", "level_gold": "textures/entity/villager2/levels/level_gold", "level_emerald": "textures/entity/villager2/levels/level_emerald", "level_diamond": "textures/entity/villager2/levels/level_diamond", "enchanted": "textures/ptd/lm_hc/misc/enchanted_entity_glint"}, "geometry": {"default": "geometry.ptd_lmhc.villager_v2"}, "scripts": {"scale": "0.9375", "pre_animation": ["variable.num_professions = 15;", "variable.num_tiers = 3;", "variable.profession_index = (query.variant < variable.num_professions ? query.variant : 0);", "variable.level_index = query.trade_tier;"]}, "animations": {"general": "animation.villager.general", "look_at_target": "animation.common.look_at_target", "move": "animation.villager.move", "baby_transform": "animation.villager.baby_transform", "get_in_bed": "animation.villager.get_in_bed"}, "animation_controllers": [{"general": "controller.animation.villager_v2.general"}, {"move": "controller.animation.villager_v2.move"}, {"baby": "controller.animation.villager_v2.baby"}], "render_controllers": ["controller.render.ptd_lmhc.villager_v2_base", "controller.render.ptd_lmhc.villager_v2_masked", "controller.render.ptd_lmhc.villager_v2_level", "controller.render.ptd_lmhc.enchanted_entity"], "spawn_egg": {"texture": "spawn_egg", "texture_index": 14}}}}