import { EntityQueryOptions } from "@minecraft/server"


/**
 * Collection of EntityQueryOptions for optimized entity filtering.
 * These presets help reduce unnecessary entity processing in different scenarios.
 */

/**
 * Excludes all non-combat related entities except players.
 * Categories excluded:
 * - Items and XP orbs
 * - All projectile types
 * - Vehicles (boats and minecarts)
 * - Technical entities (falling blocks, paintings, etc.)
 * - Passive mobs (animals, villagers)
 * - Neutral mobs (wolves, bees, etc.)
 * - Hostile mobs (monsters)
 */
export const excludedEntities: EntityQueryOptions = {
    excludeTypes: [
        // Items & Experience
        'minecraft:xp_orb',
        'minecraft:item',

        // Projectiles
        'minecraft:arrow',
        'minecraft:dragon_fireball',
        'minecraft:egg',
        'minecraft:ender_pearl',
        'minecraft:fireball',
        'minecraft:fishing_hook',
        'minecraft:lingering_potion',
        'minecraft:llama_spit',
        'minecraft:small_fireball',
        'minecraft:snowball',
        'minecraft:splash_potion',
        'minecraft:thrown_trident',
        'minecraft:wither_skull',
        'minecraft:wither_skull_dangerous',

        // Vehicles & Transportation
        'minecraft:boat',
        'minecraft:chest_boat',
        'minecraft:minecart',
        'minecraft:chest_minecart',
        'minecraft:hopper_minecart',
        'minecraft:tnt_minecart',
        'minecraft:furnace_minecart',
        'minecraft:command_block_minecart',

        // Falling Blocks & Technical
        'minecraft:falling_block',
        'minecraft:armor_stand',
        'minecraft:painting',
        'minecraft:leash_knot',
        'minecraft:item_frame',
        'minecraft:glow_item_frame',
        'minecraft:ender_crystal',
        'minecraft:eye_of_ender_signal',
        'minecraft:evocation_fang',
        'minecraft:fireworks_rocket',
        'minecraft:shulker_bullet',
        'minecraft:area_effect_cloud',
        'minecraft:lightning_bolt',

        // Passive Mobs
        'minecraft:allay',
        'minecraft:axolotl',
        'minecraft:bat',
        'minecraft:cat',
        'minecraft:chicken',
        'minecraft:cod',
        'minecraft:cow',
        'minecraft:dolphin',
        'minecraft:donkey',
        'minecraft:fox',
        'minecraft:frog',
        'minecraft:glow_squid',
        'minecraft:goat',
        'minecraft:horse',
        'minecraft:llama',
        'minecraft:mooshroom',
        'minecraft:mule',
        'minecraft:ocelot',
        'minecraft:parrot',
        'minecraft:pig',
        'minecraft:polar_bear',
        'minecraft:pufferfish',
        'minecraft:rabbit',
        'minecraft:salmon',
        'minecraft:sheep',
        'minecraft:skeleton_horse',
        'minecraft:snow_golem',
        'minecraft:squid',
        'minecraft:strider',
        'minecraft:tropical_fish',
        'minecraft:turtle',
        'minecraft:villager',
        'minecraft:wandering_trader',
        'minecraft:wolf',

        // Neutral Mobs
        'minecraft:bee',
        'minecraft:dolphin',
        'minecraft:goat',
        'minecraft:iron_golem',
        'minecraft:llama',
        'minecraft:panda',
        'minecraft:polar_bear',
        'minecraft:trader_llama',
        'minecraft:wolf',
        'minecraft:zombified_piglin',

        // Hostile Mobs
        'minecraft:bogged',
        'minecraft:blaze',
        'minecraft:breeze',
        'minecraft:cave_spider',
        'minecraft:creeper',
        'minecraft:drowned',
        'minecraft:elder_guardian',
        'minecraft:enderman',
        'minecraft:endermite',
        'minecraft:evoker',
        'minecraft:ghast',
        'minecraft:guardian',
        'minecraft:hoglin',
        'minecraft:husk',
        'minecraft:magma_cube',
        'minecraft:phantom',
        'minecraft:piglin',
        'minecraft:piglin_brute',
        'minecraft:pillager',
        'minecraft:ravager',
        'minecraft:shulker',
        'minecraft:silverfish',
        'minecraft:skeleton',
        'minecraft:slime',
        'minecraft:spider',
        'minecraft:stray',
        'minecraft:vex',
        'minecraft:vindicator',
        'minecraft:witch',
        'minecraft:wither_skeleton',
        'minecraft:zoglin',
        'minecraft:zombie',
        'minecraft:zombie_villager',
        'minecraft:zombified_piglin'
    ],
    excludeFamilies: [
        'inanimate',  // Exclude entities classified under the 'inanimate' family
        'arrow',      // Exclude all arrow types
        'boat',       // Exclude all boat types
        'minecart'    // Exclude all minecart types
    ]
}

/**
 * Similar to excludedEntities but also excludes players.
 * Use this when you need to filter out ALL entities including players.
 * Contains the same exclusion categories as excludedEntities plus:
 * - Players
 */
export const excludedEntitiesWithPlayer: EntityQueryOptions = {
    excludeTypes: [
        'minecraft:player',
        // Items & Experience
        'minecraft:xp_orb',
        'minecraft:item',

        // Projectiles
        'minecraft:arrow',
        'minecraft:dragon_fireball',
        'minecraft:egg',
        'minecraft:ender_pearl',
        'minecraft:fireball',
        'minecraft:fishing_hook',
        'minecraft:lingering_potion',
        'minecraft:llama_spit',
        'minecraft:small_fireball',
        'minecraft:snowball',
        'minecraft:splash_potion',
        'minecraft:thrown_trident',
        'minecraft:wither_skull',
        'minecraft:wither_skull_dangerous',

        // Vehicles & Transportation
        'minecraft:boat',
        'minecraft:chest_boat',
        'minecraft:minecart',
        'minecraft:chest_minecart',
        'minecraft:hopper_minecart',
        'minecraft:tnt_minecart',
        'minecraft:furnace_minecart',
        'minecraft:command_block_minecart',

        // Falling Blocks & Technical
        'minecraft:falling_block',
        'minecraft:armor_stand',
        'minecraft:painting',
        'minecraft:leash_knot',
        'minecraft:item_frame',
        'minecraft:glow_item_frame',
        'minecraft:ender_crystal',
        'minecraft:eye_of_ender_signal',
        'minecraft:evocation_fang',
        'minecraft:fireworks_rocket',
        'minecraft:shulker_bullet',
        'minecraft:area_effect_cloud',
        'minecraft:lightning_bolt',

        // Passive Mobs
        'minecraft:allay',
        'minecraft:axolotl',
        'minecraft:bat',
        'minecraft:cat',
        'minecraft:chicken',
        'minecraft:cod',
        'minecraft:cow',
        'minecraft:dolphin',
        'minecraft:donkey',
        'minecraft:fox',
        'minecraft:frog',
        'minecraft:glow_squid',
        'minecraft:goat',
        'minecraft:horse',
        'minecraft:llama',
        'minecraft:mooshroom',
        'minecraft:mule',
        'minecraft:ocelot',
        'minecraft:parrot',
        'minecraft:pig',
        'minecraft:polar_bear',
        'minecraft:pufferfish',
        'minecraft:rabbit',
        'minecraft:salmon',
        'minecraft:sheep',
        'minecraft:skeleton_horse',
        'minecraft:snow_golem',
        'minecraft:squid',
        'minecraft:strider',
        'minecraft:tropical_fish',
        'minecraft:turtle',
        'minecraft:villager',
        'minecraft:wandering_trader',
        'minecraft:wolf',

        // Neutral Mobs
        'minecraft:bee',
        'minecraft:dolphin',
        'minecraft:goat',
        'minecraft:iron_golem',
        'minecraft:llama',
        'minecraft:panda',
        'minecraft:polar_bear',
        'minecraft:trader_llama',
        'minecraft:wolf',
        'minecraft:zombified_piglin',

        // Hostile Mobs
        'minecraft:bogged',
        'minecraft:blaze',
        'minecraft:breeze',
        'minecraft:cave_spider',
        'minecraft:creeper',
        'minecraft:drowned',
        'minecraft:elder_guardian',
        'minecraft:enderman',
        'minecraft:endermite',
        'minecraft:evoker',
        'minecraft:ghast',
        'minecraft:guardian',
        'minecraft:hoglin',
        'minecraft:husk',
        'minecraft:magma_cube',
        'minecraft:phantom',
        'minecraft:piglin',
        'minecraft:piglin_brute',
        'minecraft:pillager',
        'minecraft:ravager',
        'minecraft:shulker',
        'minecraft:silverfish',
        'minecraft:skeleton',
        'minecraft:slime',
        'minecraft:spider',
        'minecraft:stray',
        'minecraft:vex',
        'minecraft:vindicator',
        'minecraft:witch',
        'minecraft:wither_skeleton',
        'minecraft:zoglin',
        'minecraft:zombie',
        'minecraft:zombie_villager',
        'minecraft:zombified_piglin'
    ],
    excludeFamilies: [
        'inanimate',  // Exclude entities classified under the 'inanimate' family
        'arrow',      // Exclude all arrow types
        'boat',       // Exclude all boat types
        'minecart'    // Exclude all minecart types
    ]
}

/**
 * Specifically targets monster-type entities for player interactions.
 * Uses the 'monster' family tag for efficient filtering.
 * Useful for combat-related features or hostile mob detection.
 */
export const excludedEntitiesPlayerForMonsters: EntityQueryOptions = {
    families: ['monster']
}

/**
 * Targets all mob-type entities for player interactions.
 * Uses the 'mob' family tag to include both hostile and passive mobs.
 * Useful for general mob detection or mob-related features.
 */
export const excludedEntitiesPlayerForMobs: EntityQueryOptions = {
    families: ['mob']
}

/**
 * Excludes technical entities while allowing all living entities including players.
 * Categories excluded:
 * - Items and XP orbs
 * - All projectile types
 * - Vehicles (boats and minecarts)
 * - Technical entities (falling blocks, paintings, etc.)
 * 
 * Useful for:
 * - Detecting all living entities in an area
 * - Applying effects to all mobs and players
 * - Monitoring entity interactions
 */
export const excludedEntitiesPlayerForAll: EntityQueryOptions = {
    excludeTypes: [
        // Items & Experience
        'minecraft:xp_orb',
        'minecraft:item',

        // Projectiles
        'minecraft:arrow',
        'minecraft:dragon_fireball',
        'minecraft:egg',
        'minecraft:ender_pearl',
        'minecraft:fireball',
        'minecraft:fishing_hook',
        'minecraft:lingering_potion',
        'minecraft:llama_spit',
        'minecraft:small_fireball',
        'minecraft:snowball',
        'minecraft:splash_potion',
        'minecraft:thrown_trident',
        'minecraft:wither_skull',
        'minecraft:wither_skull_dangerous',

        // Vehicles & Transportation
        'minecraft:boat',
        'minecraft:chest_boat',
        'minecraft:minecart',
        'minecraft:chest_minecart',
        'minecraft:hopper_minecart',
        'minecraft:tnt_minecart',
        'minecraft:furnace_minecart',
        'minecraft:command_block_minecart',

        // Technical Entities
        'minecraft:falling_block',
        'minecraft:armor_stand',
        'minecraft:painting',
        'minecraft:leash_knot',
        'minecraft:item_frame',
        'minecraft:glow_item_frame',
        'minecraft:ender_crystal',
        'minecraft:eye_of_ender_signal',
        'minecraft:evocation_fang',
        'minecraft:fireworks_rocket',
        'minecraft:shulker_bullet',
        'minecraft:area_effect_cloud',
        'minecraft:lightning_bolt'
    ],
    excludeFamilies: [
        'inanimate',
        'arrow',
        'boat',
        'minecart'
    ]
}