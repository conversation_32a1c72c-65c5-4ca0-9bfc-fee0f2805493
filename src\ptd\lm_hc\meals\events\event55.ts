import { BlockPermutation, Block, Player, Vector3, system } from '@minecraft/server';

/**
 * Event 55: <PERSON>
 * Turns all nearby lava blocks into gold blocks in a 32x32x32 area around the player
 * @param player The player who triggered the event
 */
export async function event55(player: Player): Promise<void> {
  try {
    // Define the search area (32x32x32 centered on player)
    const radius: number = 16; // 16 blocks in each direction = 32x32x32 total
    const playerPos: Vector3 = player.location;
    const startPos: Vector3 = {
      x: Math.floor(playerPos.x) - radius,
      y: Math.floor(playerPos.y) - radius,
      z: Math.floor(playerPos.z) - radius
    };
    const endPos: Vector3 = {
      x: Math.floor(playerPos.x) + radius,
      y: Math.floor(playerPos.y) + radius,
      z: Math.floor(playerPos.z) + radius
    };

    // Get the gold block permutation for replacement
    const goldBlockPerm: BlockPermutation = BlockPermutation.resolve('minecraft:gold_block');

    // Track number of blocks converted for feedback
    let blocksConverted: number = 0;
    const maxBlocksPerTick: number = 100;

    // Define lava block types to check for
    const lavaBlockIds: string[] = ['minecraft:lava', 'minecraft:flowing_lava'];

    // Process blocks in chunks to avoid performance issues
    const processBlocksQueue: Vector3[] = [];

    // First pass: identify all lava blocks in the area
    for (let x = startPos.x; x <= endPos.x; x++) {
      for (let y = startPos.y; y <= endPos.y; y++) {
        for (let z = startPos.z; z <= endPos.z; z++) {
          const pos: Vector3 = { x, y, z };
          const block: Block | undefined = player.dimension.getBlock(pos);

          if (block && lavaBlockIds.includes(block.type.id)) {
            processBlocksQueue.push(pos);
          }
        }
      }
    }

    // Set up a runnable to process blocks over time
    let index = 0;

    const processChunk = () => {
      let processed = 0;

      while (index < processBlocksQueue.length && processed < maxBlocksPerTick) {
        const pos: Vector3 = processBlocksQueue[index]!;
        const block: Block | undefined = player.dimension.getBlock(pos);

        if (block && lavaBlockIds.includes(block.type.id)) {
          block.setPermutation(goldBlockPerm);
          player.dimension.spawnParticle('minecraft:villager_happy', {
            x: pos.x + 0.5,
            y: pos.y + 0.5,
            z: pos.z + 0.5
          });
          blocksConverted++;
        }

        index++;
        processed++;
      }

      // Continue processing if there are more blocks
      if (index < processBlocksQueue.length) {
        system.runTimeout(processChunk, 1);
      } else {
        // All done, give feedback to player
        if (blocksConverted > 0) {
          player.sendMessage(`§6Lucky Lava§r: Transformed §e${blocksConverted}§r lava blocks into gold!`);
        } else {
          player.sendMessage(`§6Lucky Lava§r: No lava blocks found nearby.`);
        }
      }
    };

    // Start processing blocks
    if (processBlocksQueue.length > 0) {
      player.dimension.playSound('random.levelup', player.location);
      processChunk();
    } else {
      player.sendMessage(`§6Lucky Lava§r: No lava blocks found nearby.`);
    }
  } catch (error) {
  }
  return;
}
