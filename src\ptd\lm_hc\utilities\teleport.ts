import { Entity, system, Vector3 } from "@minecraft/server";

/**
 * Teleports an entity to a specified location and plays a sound effect.
 * 
 * @param entity - The entity to be teleported
 * @param location - The destination coordinates as a Vector3
 * @returns A promise that resolves when the teleportation is complete
 * @throws Will log a warning message if teleportation fails
 * 
 * @remarks
 * This function will:
 * 1. Teleport the entity to the specified location
 * 2. Wait for 1 tick
 * 3. Play an enderman portal sound at the destination
 */
export async function teleport(entity: Entity, location: Vector3): Promise<void> {
    try {
        entity.teleport(location);
        await system.waitTicks(1);
        entity.dimension.playSound('mob.endermen.portal', location);
    }
    catch (error) {
    }
}