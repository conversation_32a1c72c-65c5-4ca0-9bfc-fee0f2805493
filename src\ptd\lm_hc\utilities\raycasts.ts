import { Vector3 } from "@minecraft/server";

/**
 * Fixed position raycast
 * Creates a raycast between two fixed positions and returns all points along the path.
 * @param from Starting position of the raycast
 * @param to End position of the raycast
 * @param distance Total distance to travel
 * @param step Distance between each point
 * @returns Array of Vector3 positions along the raycast path
 */
export function fixedPosRaycast(from: Vector3, to: Vector3, distance: number, step: number): Vector3[] {
    const positions: Vector3[] = [];

    for (let i = 0; i <= distance; i += step) {
        const t = i / distance;
        const pos: Vector3 = {
            x: from.x + t * (to.x - from.x),
            y: from.y + t * (to.y - from.y),
            z: from.z + t * (to.z - from.z)
        }
        try {
            positions.push(pos);
        } catch (e) { }
    }
    return positions;
}

/**
 * Fixed length raycast
 * Creates a directional raycast from a starting point along a vector direction.
 * @param entity The entity initiating the raycast
 * @param from Starting position of the raycast
 * @param direction Direction vector of the raycast
 * @param length Maximum length of the raycast
 * @param step Distance between each point
 * @returns Array of Vector3 positions along the raycast path
 */
export function fixedLenRaycast(from: Vector3, direction: Vector3, length: number, step: number): Vector3[] {
    const positions: Vector3[] = [];

    for (let i = 0; i <= length; i += step) {
        const particleLoc: Vector3 = {
            x: from.x + direction.x * i,
            y: from.y + direction.y * i,
            z: from.z + direction.z * i
        };
        try {
            positions.push(particleLoc);
        } catch (e) {
            continue;
        }
    }
    return positions;
}