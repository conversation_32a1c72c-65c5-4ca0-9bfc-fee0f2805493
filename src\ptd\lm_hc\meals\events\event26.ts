import { BlockTypes, Player, Vector3, system } from '@minecraft/server';
import { PROTECTED_BLOCKS } from '../../utilities/constants/protectedBlocks';

/**
 * Event 26: Exploding Blocks
 * Changes random nearby blocks into TNT and instantly detonates them using an interval system
 * Processes 1-5 random blocks per tick for a chaotic explosion effect
 * Tracks processed blocks and cleans up all resources when the function exits
 * @param player The player who triggered the event
 */
export function event26(player: Player): void {
  const dimension = player.dimension;
  const startPos: Vector3 = player.location;
  const radius = 5;

  // Create arrays to store block positions for processing
  const positions: Vector3[] = [];

  // Gather all valid block positions first
  for (let x = -radius; x <= radius; x++) {
    for (let y = -radius; y <= radius; y++) {
      for (let z = -radius; z <= radius; z++) {
        const pos: Vector3 = {
          x: Math.floor(startPos.x + x),
          y: Math.floor(startPos.y + y),
          z: Math.floor(startPos.z + z)
        };
        positions.push(pos);
      }
    }
  }

  // Keep track of processed positions to avoid duplicates
  const processedPositions: Set<string> = new Set();

  // Maximum number of blocks to process (to ensure the interval eventually stops)
  const maxBlocksToProcess = positions.length;
  let processedCount = 0;

  // Track when we should terminate the interval (explosion completed or function exited)
  let shouldTerminate = false;

  // Helper function to clean up resources
  const cleanup = () => {
    if (intervalId) {
      system.clearRun(intervalId);
    }
    // Clear the sets to help garbage collection
    processedPositions.clear();
    // Set the flag to prevent further processing
    shouldTerminate = true;
  };

  // Register a safety timeout to ensure the interval is cleared if not done naturally
  // This prevents potential memory leaks if something unexpected happens
  const safetyTimeoutId = system.runTimeout(() => {
    cleanup();
  }, 30 * 20); // 30 seconds maximum runtime (20 ticks per second)

  // Create an interval to process blocks
  const intervalId = system.runInterval(() => {
    try {
      // Check if we should terminate early
      if (shouldTerminate) {
        cleanup();
        return;
      }

      // Process a random number (1-5) of blocks per tick
      const blocksToProcess = Math.floor(Math.random() * 5) + 1;

      for (let i = 0; i < blocksToProcess; i++) {
        // Check if we've processed all possible blocks
        if (processedCount >= maxBlocksToProcess || processedPositions.size >= positions.length) {
          // Clear safety timeout since we're completing normally
          system.clearRun(safetyTimeoutId);
          cleanup();
          return;
        }

        // Select a random block position that hasn't been processed yet
        let randomIndex: number;
        let positionKey: string = '';
        let attempts = 0;
        const maxAttempts = 20; // Prevent infinite loops
        let position: Vector3 | undefined;

        do {
          randomIndex = Math.floor(Math.random() * positions.length);
          position = positions[randomIndex];

          // Skip this iteration if position is undefined
          if (!position) {
            attempts++;
            continue;
          }

          positionKey = `${position.x},${position.y},${position.z}`;
          attempts++;
        } while (processedPositions.has(positionKey) && attempts < maxAttempts);

        // If we can't find an unprocessed position after max attempts or position is undefined, skip
        if (attempts >= maxAttempts || !position) {
          continue;
        }

        const blockPos = position;
        processedPositions.add(positionKey);
        processedCount++;

        // Get the block at this position
        const block = dimension.getBlock(blockPos);
        if (!block) continue;

        // Skip protected blocks non-solid blocks
        // Check if the block is air, liquid, or a protected block
        if (block.isAir || block.isLiquid || PROTECTED_BLOCKS.has(block.typeId)) {
          continue;
        }

        try {
          // Set the block to air first
          dimension.setBlockType(blockPos, BlockTypes.get('minecraft:air')!);

          // Spawn TNT at the position
          dimension.spawnEntity('minecraft:tnt', blockPos);

          // Play placement sound
          dimension.playSound('dig.grass', blockPos);
        } catch (blockError) {
          // Silently handle individual block errors
        }
      }
    } catch (error) {
      // Clear safety timeout and cleanup on error
      system.clearRun(safetyTimeoutId);
      cleanup();
    }
  });

  // Return immediately, the interval will handle the processing
  return;
}
