// Potions Galore - Gives the player long beneficial potions

import { Player } from '@minecraft/server';

/**
 * Event 10: Potions Galore - Gives the player a selection of long duration beneficial potions.
 * The potions are granted through a loot table that contains various
 * beneficial effects like Speed, Strength, Regeneration etc.
 *
 * @param player - The player who will receive the potions
 * @throws Will log a warning message if the event execution fails
 *
 * @remarks
 * - Grants potions through the event11 loot table
 * - All potions are long duration variants
 * - Includes effects like:
 *   - Speed
 *   - Jump Boost
 *   - Night Vision
 *   - Invisibility
 *   - Water Breathing
 *   - Strength
 *   - Regeneration
 *   - Fire Resistance
 *   - Slow Falling
 */
export function event11(player: Player): void {
  try {
    player.dimension.runCommand(`/loot give @p[name="${player.name}"] loot "ptd/lm_hc/event11"`);
    player.dimension.runCommand(`/playsound random.levelup @p[name="${player.name}"]`);
  } catch (error) {
  }
}
