import { EquipmentSlot, EffectTypes, EntityComponentTypes } from '@minecraft/server';
import { onEatMeal } from '../events';
/**
 * Event 28. Cursed Pickaxe: Curses the currently held pickaxe
 *
 * If the player is holding a pickaxe, it gets cursed:
 * - Renamed with "Cursed" prefix
 * - Adds lore describing mining fatigue effect
 * - The item keeps the curse permanently
 *
 * @param player - The player whose held pickaxe will be cursed
 * @throws Will throw if unable to access player components or modify item
 */
export function event28(player) {
    try {
        const inventory = player.getComponent(EntityComponentTypes.Inventory);
        if (!inventory) {
            return;
        }
        // Find all pickaxes in the inventory
        const pickaxes = [];
        const container = inventory.container;
        if (!container) {
            player.sendMessage('§cCould not access your inventory!');
            return;
        }
        // Search through all inventory slots
        for (let i = 0; i < container.size; i++) {
            const item = container.getItem(i);
            if (item && item.typeId.includes('pickaxe')) {
                pickaxes.push({ item, slot: i });
            }
        }
        if (pickaxes.length === 0) {
            onEatMeal(player);
            return;
        }
        // Select a random pickaxe from the inventory
        const randomIndex = Math.floor(Math.random() * pickaxes.length);
        const selectedPickaxeInfo = pickaxes[randomIndex];
        if (!selectedPickaxeInfo) {
            return;
        }
        const { item: selectedPickaxe, slot: selectedSlot } = selectedPickaxeInfo;
        // Parse the pickaxe type from the ID
        const pickaxeType = selectedPickaxe.typeId.split(':')[1]; // e.g. "diamond_pickaxe"
        if (!pickaxeType) {
            return;
        }
        const materialType = pickaxeType.split('_')[0]; // e.g. "diamond"
        if (!materialType) {
            return;
        }
        // Capitalize the material type for display
        const capitalizedType = materialType.charAt(0).toUpperCase() + materialType.slice(1);
        // Curse the pickaxe
        selectedPickaxe.nameTag = `Cursed ${capitalizedType} Pickaxe`;
        selectedPickaxe.setLore([
            '§cCursed with Mining Fatigue',
            '§7This pickaxe has been cursed,',
            '§7slowing down your mining speed',
            '§7significantly while held.'
        ]);
        // Set the dynamic property to mark it as cursed
        selectedPickaxe.setDynamicProperty('isCursed', true);
        // Update the item in the player's inventory
        container.setItem(selectedSlot, selectedPickaxe);
        player.sendMessage(`§7Your ${capitalizedType} Pickaxe has been cursed!`);
    }
    catch (error) {
    }
}
/**
 * Checks if a player is holding a cursed pickaxe and applies mining fatigue if they are.
 * This function should be called every tick from the main loop.
 *
 * @param player - The player to check for cursed pickaxe effect
 */
export function checkCursedPickaxe(player) {
    try {
        const equippable = player.getComponent('equippable');
        if (!equippable) {
            return;
        }
        const mainhandItem = equippable.getEquipment(EquipmentSlot.Mainhand);
        if (mainhandItem && mainhandItem.typeId.includes('pickaxe')) {
            // Check for a dynamic property that indicates the pickaxe is cursed
            const isCursed = mainhandItem.getDynamicProperty('isCursed');
            if (isCursed) {
                const miningFatigue = EffectTypes.get('mining_fatigue');
                if (miningFatigue) {
                    player.addEffect(miningFatigue, 20, { amplifier: 2 });
                }
            }
        }
    }
    catch (error) {
    }
    return;
}
