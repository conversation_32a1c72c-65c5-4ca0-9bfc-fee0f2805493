import { system } from '@minecraft/server';
import { summonEntity } from '../../utilities/summonEntity';
import { getRandomInt } from '../../utilities/rng';
import { getRandomLocation } from '../../utilities/vector3';
/**
 * Event 2. TNT Rain: Creates a rain of TNT entities around the player for 10 seconds.
 * TNT entities spawn at random locations above the player with particle effects.
 * The event runs for exactly 10 seconds with TNT spawning every 5 ticks.
 *
 * @param player - The Player instance for whom to spawn the TNT rain
 * @remarks
 * - Spawns TNT at random positions above the player
 * - Each TNT is accompanied by an explosion particle effect
 * - Runs for exactly 10 seconds (200 ticks)
 * - TNT spawns every 5 ticks
 *
 * @throws Logs a warning to console if the event execution fails
 */
export function event2(player) {
    try {
        let tickCount = 0;
        const DURATION_TICKS = 200; // 10 seconds * 20 ticks/second
        const runId = system.runInterval(() => {
            if (tickCount >= DURATION_TICKS) {
                system.clearRun(runId);
                return;
            }
            const startingLoc = player.getHeadLocation();
            const randomXZOffset = getRandomInt(-16, 16);
            const randomYOffset = getRandomInt(4, 16);
            const location = getRandomLocation(startingLoc, player.dimension, 0, randomXZOffset, randomYOffset, true);
            if (location) {
                summonEntity('minecraft:tnt', player.dimension, location);
                player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', location);
                player.dimension.playSound('mob.endermen.portal', location);
            }
            tickCount += 5;
        }, 5);
    }
    catch (error) {
    }
}
