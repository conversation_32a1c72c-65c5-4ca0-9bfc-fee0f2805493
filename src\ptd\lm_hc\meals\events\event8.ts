import { Player } from '@minecraft/server';
import { getRandomLocation } from '../../utilities/vector3';

/**
 * Event 8. Wither Summon: Instantly spawns a Wither boss.
 *
 * Finds a safe air block near the player to spawn the Wither
 * Base offset: 4 blocks
 * Additional random offset: 2 blocks
 * Y offset: 0 blocks (same level as player)
 *
 * @param player - The player near whom to spawn the Wither
 * @throws Will throw if unable to access player dimension or location, or if no safe spawn location is found
 */
export function event8(player: Player): void {
  try {
    const dimension = player.dimension;
    const playerPos = player.location;

    // Find a safe location near the player with air block validation
    // Using slightly larger offset for the Wither as it's more dangerous
    const spawnPos = getRandomLocation(playerPos, dimension, 4, 2, 0, true);

    if (!spawnPos) {
      return;
    }

    // Spawn the Wither at the safe location
    dimension.spawnEntity('minecraft:wither', spawnPos);
    dimension.spawnParticle('minecraft:cauldron_explosion_emitter', spawnPos);
    dimension.playSound('mob.endermen.portal', spawnPos);
  } catch (error) {
  }
  return;
}
