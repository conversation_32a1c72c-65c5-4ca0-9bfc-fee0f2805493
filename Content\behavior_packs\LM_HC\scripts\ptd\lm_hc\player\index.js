import { world, ItemStack, EntityComponentTypes, EquipmentSlot } from "@minecraft/server";
import { restoreFlightOnSpawn } from "../meals/events/event29";
/**
 * Registers the event handler to give the guidebook item to a player in their main hand on first spawn.
 * Uses a persistent dynamic property to ensure it is only given once per player.
 * Uses the entity equipment component for main hand placement.
 */
export function registerFirstSpawnGuidebook() {
    world.afterEvents.playerSpawn.subscribe((ev) => {
        const player = ev.player;
        if (!player || !player)
            return;
        // Restore flight state if player had active flight mode
        restoreFlightOnSpawn(player);
        // Use a persistent dynamic property to track if the player has received the guidebook
        const HAS_GUIDEBOOK_PROP = "ptd_lmhc:has_guidebook";
        if (player.getDynamicProperty(HAS_GUIDEBOOK_PROP))
            return;
        // Get the player's equippable component
        const equippable = player.getComponent(EntityComponentTypes.Equippable);
        if (!equippable) {
            return;
        }
        // Check if the main hand is empty
        const mainHandItem = equippable.getEquipment(EquipmentSlot.Mainhand);
        if (mainHandItem !== undefined) {
            // Main hand is not empty, do not overwrite
            return;
        }
        // Create the guidebook item stack
        const guidebookItem = new ItemStack("ptd_lmhc:guidebook", 1);
        // Set the guidebook in the main hand
        equippable.setEquipment(EquipmentSlot.Mainhand, guidebookItem);
        player.setDynamicProperty(HAS_GUIDEBOOK_PROP, true);
    });
}
