import { getRandomLocation } from '../../utilities/vector3';
import { spawnEntitiesWithInterval } from '../../utilities/summonEntity';
// Constants for mob horde
const SPAWN_DELAY_TICKS = 2; // 0.1 seconds between spawns
const MIN_MOB_COUNT = 8; // Minimum number of mobs to spawn
const MAX_MOB_COUNT = 16; // Maximum number of mobs to spawn
const DEFAULT_MOB_TYPE = 'minecraft:zombie'; // Default mob type if array selection fails
/**
 * Array of hostile mob types to choose from
 * Excludes enderman, warden, ender dragon, and the wither as specified
 */
const HOSTILE_MOBS = [
    'minecraft:zombie',
    'minecraft:skeleton',
    'minecraft:spider',
    'minecraft:cave_spider',
    'minecraft:creeper',
    'minecraft:witch',
    'minecraft:slime',
    'minecraft:stray',
    'minecraft:husk',
    'minecraft:drowned',
    'minecraft:phantom',
    'minecraft:zombie_villager',
    'minecraft:pillager',
    'minecraft:vindicator',
    'minecraft:evoker',
    'minecraft:vex',
    'minecraft:ravager',
    'minecraft:blaze',
    'minecraft:magma_cube',
    'minecraft:ghast',
    'minecraft:piglin',
    'minecraft:piglin_brute',
    'minecraft:zombified_piglin',
    'minecraft:hoglin',
    'minecraft:zoglin',
    'minecraft:guardian',
    'minecraft:elder_guardian',
    'minecraft:shulker',
    'minecraft:silverfish',
    'minecraft:wither_skeleton',
    'minecraft:endermite',
    'minecraft:breeze',
    'minecraft:bogged'
];
/**
 * Properly pluralizes a mob name following English grammar rules
 * @param name The singular mob name
 * @returns The properly pluralized name
 */
function pluralizeMobName(name) {
    // Handle special cases
    const specialCases = {
        'witch': 'witches',
        'wolf': 'wolves',
        'magma cube': 'magma cubes',
        'bogged': 'bogged',
        'undead': 'undead',
        'sheep': 'sheep',
        'fish': 'fish',
        'elder guardian': 'elder guardians',
        'endermite': 'endermites',
        'vex': 'vexes'
    };
    // Check for special cases first
    if (specialCases[name]) {
        return specialCases[name];
    }
    // Apply standard English pluralization rules
    if (name.endsWith('y')) {
        // e.g., 'fly' -> 'flies'
        return name.slice(0, -1) + 'ies';
    }
    else if (name.endsWith('s') ||
        name.endsWith('x') ||
        name.endsWith('z') ||
        name.endsWith('ch') ||
        name.endsWith('sh')) {
        // e.g., 'box' -> 'boxes', 'bush' -> 'bushes'
        return name + 'es';
    }
    else {
        // Default case: add 's'
        return name + 's';
    }
}
/**
 * Event 60: Mob Horde
 * Spawns a horde of 8 to 16 hostile mobs around the player
 * @param player The player who triggered the event
 */
export function event60(player) {
    try {
        // Select a random mob type from the array
        const randomMobIndex = Math.floor(Math.random() * HOSTILE_MOBS.length);
        const randomMobType = HOSTILE_MOBS[randomMobIndex] || DEFAULT_MOB_TYPE;
        // Determine random quantity between MIN_MOB_COUNT and MAX_MOB_COUNT
        const mobCount = Math.floor(Math.random() * (MAX_MOB_COUNT - MIN_MOB_COUNT + 1)) + MIN_MOB_COUNT;
        // Configure the entity type and quantity to spawn
        const entityConfigs = [{ entityId: randomMobType, count: mobCount }];
        // Play initial sound effect
        player.dimension.playSound('mob.endermen.portal', player.location, {
            volume: 1.0,
            pitch: 0.7 + Math.random() * 0.3
        });
        // Create a location callback function that returns a random position around the player
        const getSpawnLocation = () => {
            const spawnPos = getRandomLocation(player.location, player.dimension, 3, // minDistance
            5, // additionalOffset (maxDistance = 8)
            0, // no height offset
            true // check for air block
            );
            // Play particle effect at spawn location if position is valid
            if (spawnPos) {
                player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', spawnPos);
                player.dimension.playSound('mob.endermen.portal', spawnPos, {
                    volume: 0.5,
                    pitch: 0.8 + Math.random() * 0.4
                });
            }
            return spawnPos;
        };
        // Define a callback to be executed when a mob is spawned
        const onMobSpawned = (entity) => {
            try {
                // No need to set target - hostile mobs will naturally target nearby players
                // Just verify the entity spawned successfully
                if (!entity || !entity) {
                }
            }
            catch (error) {
            }
        };
        // Start spawning mobs with the specified delay between each
        spawnEntitiesWithInterval(player.dimension, entityConfigs, getSpawnLocation, SPAWN_DELAY_TICKS, onMobSpawned).catch((_) => {
        });
        // Get clean mob name without namespace and with spaces instead of underscores
        const rawMobName = randomMobType
            ? randomMobType.replace('minecraft:', '').replace(/_/g, ' ')
            : 'hostile mob';
        // Properly pluralize the mob name
        const pluralizedMobName = pluralizeMobName(rawMobName);
        // Send message to player
        player.sendMessage(`§c⚔️ A horde of ${mobCount} ${pluralizedMobName} has appeared! §c⚔️`);
    }
    catch (error) {
    }
    return;
}
